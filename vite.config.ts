import fs from 'node:fs'
import path from 'node:path'
import uni from '@dcloudio/vite-plugin-uni'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig } from 'vite'
import { UnifiedViteWeappTailwindcssPlugin } from 'weapp-tailwindcss/vite'
import { WeappTailwindcssDisabled } from './platform'
import postcssPlugins from './postcss.config'

// 创建移除 uni-app CDN 路径的插件
function removeUniCdnPathPlugin() {
  return {
    name: 'remove-uni-cdn-path',
    writeBundle() {
      const pathReg = /\s*background(-image)?:\s*url\(\S*?\/\/[\w.-]*?dcloud.net.cn\S*?\);?[ \t]*/gi
      // eslint-disable-next-line node/prefer-global/process
      const outputDir = process.env.UNI_OUTPUT_DIR || 'dist'

      const eachReplace = (dirPath: string) => {
        if (!fs.existsSync(dirPath)) { return }

        fs.readdirSync(dirPath, { withFileTypes: true }).forEach((dirent) => {
          const filePath = path.join(dirPath, dirent.name)
          if (dirent.isDirectory()) {
            eachReplace(filePath)
          }
          else if (dirent.isFile() && /\.(?:c|ac|wx)ss$/.test(dirent.name)) {
            try {
              const fileData = fs.readFileSync(filePath, 'utf8')
              if (pathReg.test(fileData)) {
                fs.writeFileSync(filePath, fileData.replace(pathReg, ''), 'utf8')
              }
            }
            catch {
              // 忽略错误
            }
          }
        })
      }

      eachReplace(outputDir)
    },
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  // uvtw 一定要放在 uni 后面
  plugins: [
    uni(),
    UnifiedViteWeappTailwindcssPlugin({
      rem2rpx: true,
      disabled: WeappTailwindcssDisabled,
    }),
    AutoImport({
      imports: ['vue', 'uni-app', 'pinia'],
      dts: './src/auto-imports.d.ts',
      eslintrc: {
        enabled: true,
      },
    }),
    removeUniCdnPathPlugin(),
  ],
  // 内联 postcss 注册 tailwindcss
  css: {
    postcss: {
      plugins: postcssPlugins,
    },
    // https://vitejs.dev/config/shared-options.html#css-preprocessoroptions
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'],
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
})
