<script setup lang="ts">
import { getApp, useAppStore } from '@/stores/app'

const props = defineProps({
  appId: {
    type: String,
    default: '',
  },
  companyId: {
    type: String,
    default: '',
  },
})

const appStore = useAppStore()
const appInfo = ref()
// 会员套餐数据
const memberPackages = ref([])

// 资源包套餐数据
const resourcePackages = ref([])

async function getPriceList() {
  const { list } = await appStore.getCompanyAppByPriceList(props.appId, props.companyId)
  appInfo.value = await appStore.getApp(props.appId)

  list.map((item: any) => {
    console.log(item)

    if (item.package_type === 'PerDay') {
      memberPackages.value.push(item)
    }
    else {
      resourcePackages.value.push(item)
    }
  })
}

// 页面加载时执行
onMounted(() => {
  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: '智能体价格表',
  })

  getPriceList()
})
</script>

<template>
  <view class="price-list-container">
    <!-- 标题区域 -->
    <view class="header-section">
      <view class="main-title">
        智能体价格表
      </view>
      <view class="subtitle">
        — PRICE LIST —
      </view>
    </view>

    <!-- 会员价格区域 -->
    <view v-if="memberPackages.length > 0" class="price-card">
      <view class="price-card-title">
        — 会员售价 —
      </view>
      <view class="price-card-subtitle" />

      <view class="price-table">
        <view class="table-header">
          <view class="table-cell w-2/6">
            套餐
          </view>
          <view class="table-cell w-2/6">
            日对话上限
          </view>
          <view class="table-cell w-1/6">
            价格(元)
          </view>
          <view class="table-cell w-1/6">
            天数
          </view>
        </view>

        <view v-for="(pkg, index) in memberPackages" :key="index" class="table-row">
          <view class="table-cell w-2/6">
            {{ pkg.name }}
          </view>
          <view class="table-cell w-2/6">
            {{ pkg.max_usage_per_day }}
          </view>
          <view class="table-cell w-1/6">
            {{ pkg.points_per_day / 100 }}
          </view>
          <view class="table-cell w-1/6">
            {{ pkg.days }}
          </view>
        </view>
      </view>
    </view>

    <!-- 资源包价格区域 -->
    <view v-if="resourcePackages.length > 0" class="price-card">
      <view class="price-card-title">
        — 资源包售价 —
      </view>
      <view class="price-card-subtitle" />

      <view class="price-table">
        <view class="table-header">
          <view class="table-cell w-2/6">
            套餐
          </view>
          <view class="table-cell w-2/6">
            次数
          </view>
          <view class="table-cell w-1/6">
            价格(元)
          </view>
          <view class="table-cell w-1/6">
            天数
          </view>
        </view>

        <view v-for="(pkg, index) in resourcePackages" :key="index" class="table-row">
          <view class="table-cell w-2/6">
            {{ pkg.name }}
          </view>
          <view class="table-cell w-2/6">
            {{ pkg.times }}
          </view>
          <view class="table-cell w-1/6">
            {{ pkg.points_per_time / 100 }}
          </view>
          <view class="table-cell w-1/6">
            {{ pkg.days }}
          </view>
        </view>
      </view>
    </view>

    <!-- 二维码区域 -->
    <view class="qrcode-section">
      <image
        v-if="appInfo?.picture_remark" :src="appInfo?.picture_remark" :show-menu-by-longpress="true"
        class="qrcode-image"
      />
      <view class="qrcode-text">
        长按二维码识别
      </view>
    </view>
  </view>
</template>

<style scoped>
.price-list-container {
  @apply flex flex-col items-center min-h-screen w-full p-1;
  background-color: #00c853; /* 绿色背景 */
  background-image: url('/static/images/pattern.svg'); /* 添加背景图案 */
  padding-bottom: 30px;
}

.header-section {
  @apply w-full flex flex-col items-center justify-center py-8;
}

.main-title {
  @apply text-3xl font-bold text-white mb-1;
}

.subtitle {
  @apply text-base text-white opacity-90;
}

.price-card {
  @apply w-full bg-white rounded-lg shadow-md mb-2 p-1 flex flex-col items-center;
}

.price-card-title {
  @apply text-lg font-medium text-gray-800 mb-1;
}

.price-card-subtitle {
  @apply text-sm text-gray-500 mb-4;
}

.price-table {
  @apply w-full;
}

.table-header {
  @apply flex w-full bg-gray-100 rounded-t-md;
}

.table-row {
  @apply flex w-full border-b border-gray-200;
}

.table-row:last-child {
  @apply border-b-0;
}

.table-cell {
  @apply py-3 px-1 text-center text-sm;
}

.qrcode-section {
  @apply flex flex-col items-center mt-4;
}

.qrcode-image {
  @apply w-32 h-32 bg-white p-2 rounded-md;
}

.qrcode-text {
  @apply text-white text-sm mt-2;
}
</style>
