<script lang="ts" setup>
import { useCompanyStore } from '@/stores/company'
import { useUsersStore } from '@/stores/user'
import { onMounted, ref } from 'vue'

const userStore = useUsersStore()
const userList = ref()
const tokenData = ref()
const companyStore = useCompanyStore()
// 数据统计
const statisticsData = ref()

// 时间范围选择
const selectedTimeRange = ref(7) // 默认7天
const startTime = ref<Date>()
const endTime = ref<Date>()

// 获取用户数据
async function fetchUsers() {
  const result = await userStore.getCurrentUserInfo()
  if (result) {
    // 获取当前公司 id
    userList.value = result
    console.log('用户数据', userList.value)
  }
}

// 计算时间范围
function calculateTimeRange(days: number) {
  const now = new Date()
  const start = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)

  // 设置结束时间为当天23:59:59
  endTime.value = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
  // 设置开始时间为指定天数前的00:00:00
  startTime.value = new Date(start.getFullYear(), start.getMonth(), start.getDate(), 0, 0, 0)
}

// 获取token数据
async function fetchCompanyToken() {
  console.log('公司id', userList.value.current_company_id)
  const result = await companyStore.getCompanyToken(userList.value.current_company_id)
  console.log('获取空间的token数据', result)
  tokenData.value = result
}

// 获取统计数据
async function fetchOperatorStatistics() {
  const result = await companyStore.getOperatorStatistics(
    startTime.value,
    endTime.value,
    undefined,
    undefined,
    userList.value.current_company_id,
  )
  console.log('获取空间的统计数据', result)
  statisticsData.value = result
}

// 切换时间范围
function switchTimeRange(days: number) {
  selectedTimeRange.value = days
  calculateTimeRange(days)
  fetchOperatorStatistics()
}
function processNumber(num: number): string | number {
  const intNum = Math.floor(num)
  if (intNum < 1000) {
    return intNum
  }
  const numInK = (intNum / 1000).toFixed(2)
  return `${numInK}k`
}

onMounted(async () => {
  // 加载用户数据
  await fetchUsers()
  // 初始化默认时间范围（7天）
  calculateTimeRange(selectedTimeRange.value)
  // 获取token数据
  await fetchCompanyToken()
  // 获取统计数据
  await fetchOperatorStatistics()
})
</script>

<template>
  <!-- 整体容器 -->
  <view class="min-h-screen bg-gray-50 pb-4">
    <!-- 时间范围选择 -->
    <view class="mx-4 mt-4 rounded-xl bg-white shadow-sm">
      <view class="p-4">
        <text class="mb-3 block text-lg font-semibold text-gray-800">时间范围</text>
        <view class="flex space-x-2">
          <view
            class="flex-1 rounded-lg px-4 py-2 text-center transition-all duration-200"
            :class="selectedTimeRange === 7 ? 'bg-blue-500 text-white shadow-md' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
            @click="switchTimeRange(7)"
          >
            <text class="text-sm font-medium">最近7天</text>
          </view>
          <view
            class="flex-1 rounded-lg px-4 py-2 text-center transition-all duration-200"
            :class="selectedTimeRange === 30 ? 'bg-blue-500 text-white shadow-md' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
            @click="switchTimeRange(30)"
          >
            <text class="text-sm font-medium">最近30天</text>
          </view>
        </view>
      </view>
    </view>

    <!-- TOKEN统计卡片 -->
    <view class="mx-4 mt-4 rounded-xl bg-white shadow-sm">
      <view class="border-b border-gray-100 p-4">
        <text class="text-lg font-semibold text-gray-800">Token统计</text>
      </view>
      <view class="grid grid-cols-3 gap-3 p-4">
        <!-- 累计收益 -->
        <view
          class="flex flex-col items-center rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 p-3 shadow"
        >
          <text class="mb-1 text-sm text-gray-600">总token</text>
          <text class="text-xl font-bold text-blue-600">{{ processNumber(tokenData?.recharge_points) }}</text>
        </view>
        <!-- 生成总数 -->
        <view
          class="flex flex-col items-center rounded-lg bg-gradient-to-br from-amber-50 to-amber-100 p-3 shadow"
        >
          <text class="mb-1 text-sm text-gray-600">消耗tokn</text>
          <text class="text-xl font-bold text-amber-600">{{ processNumber(tokenData?.consumed_points_period) }}</text>
        </view>
        <!-- 已兑换 -->
        <view
          class="flex flex-col items-center rounded-lg bg-gradient-to-br from-green-50 to-green-100 p-3 shadow"
        >
          <text class="mb-1 text-sm text-gray-600">剩余token</text>
          <text class="text-xl font-bold text-green-600">
            {{ processNumber(tokenData?.available_points)
            }}
          </text>
        </view>
      </view>
    </view>

    <!-- 数据统计卡片 -->
    <view class="mx-4 mt-4 rounded-xl bg-white shadow-sm">
      <view class="border-b border-gray-100 p-4">
        <text class="text-lg font-semibold text-gray-800">数据统计</text>
      </view>
      <view class="grid grid-cols-3 gap-3 p-4">
        <!-- 第一行卡片 -->
        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">访问次数</text>
          <text class="text-xl font-bold text-blue-600">{{ statisticsData?.visit_count }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-amber-50 to-amber-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">访问人数</text>
          <text class="text-xl font-bold text-amber-600">{{ statisticsData?.visit_users }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-green-50 to-green-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">对话次数</text>
          <text class="text-xl font-bold text-green-600">{{ statisticsData?.chat_count }}</text>
        </view>

        <!-- 第二行卡片 -->
        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">对话人数</text>
          <text class="text-xl font-bold text-purple-600">{{ processNumber(statisticsData?.chat_users) }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-pink-50 to-pink-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">人均次数</text>
          <text class="text-xl font-bold text-pink-600">{{ processNumber(statisticsData?.avg_chat_per_user) }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-yellow-50 to-yellow-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">转化率</text>
          <text class="text-xl font-bold text-yellow-600">{{ processNumber(statisticsData?.conversion_rate) }}</text>
        </view>

        <!-- 第三行卡片 -->
        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-indigo-50 to-indigo-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">兑换次数</text>
          <text class="text-xl font-bold text-indigo-600">{{ processNumber(statisticsData?.redemption_count) }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-red-50 to-red-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">兑换人数</text>
          <text class="text-xl font-bold text-red-600">{{ processNumber(statisticsData?.redemption_users) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
