<script setup lang="ts">
import { useAgentStore } from '@/stores/agent'
import { useAuthStore } from '@/stores/auth'
import AgentItem from './agentItem.vue'

const props = defineProps<{

  agentsCate: any
}>()

const emit = defineEmits(['reLoad'])
const agentStore = useAgentStore()
const authStore = useAuthStore()
const { userRoleInfo } = storeToRefs(authStore)
const collapse = ref()
const accordionVal = ref('0')
function change(e: any) {
  console.log(e, 'eee')
}

function reload(parameters) {
  emit('reLoad', parameters)
}
onMounted(() => {
  collapse?.value?.resize()

  console.log(props.agentsCate, 'props.agentsCateprops.agentsCateprops.agentsCate')
})
</script>

<template>
  <view v-if="agentsCate.type && agentsCate.list" class="flex flex-col space-y-4">
    <uni-collapse ref="collapse" v-model="accordionVal" accordion @change="change">
      <uni-collapse-item v-for="cateItem in agentsCate.list" :key="cateItem.id" :border="false" title-border="none">
        <template #title>
          <view class="collapse_title">
            {{ cateItem.name }}
          </view>
        </template>

        <template #default>
          <view class="grid gap-4 space-y-10 py-4 pl-4 ">
            <AgentItem
              v-for="item in cateItem.list" :key="item.id" class="agent-card-wrapper" :agent-item="item"
              @reLoad="reload"
            />
          </view>
        </template>
      </uni-collapse-item>
    </uni-collapse>
  </view>
  <view v-else class="grid gap-4 space-y-10">
    <AgentItem
      v-for="item in agentsCate" :key="item.id" class="agent-card-wrapper" :agent-item="item"
      @reLoad="reload"
    />
  </view>
</template>

<style scoped>
.agent-card {
  @apply flex items-center rounded-xl bg-white  shadow-sm transition-all duration-200;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.03);
}

.collapse_title {
  @apply flex items-center gap-2 before:content-[''] before:block before:w-1 before:h-6 before:bg-blue-500 pb-2 bg-transparent text-base;
}

.agent-card-wrapper {
  @apply w-full  space-y-8 grid gap-4;
}
</style>
