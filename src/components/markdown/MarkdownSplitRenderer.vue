<script setup>
import { computed } from 'vue'
import PlainText from './plain-text.vue'
import UaMarkdown from './ua-markdown/ua-markdown.vue'

const props = defineProps({
  // 解析内容
  message: String,
})

// 拆分需要markdown处理和不需要处理的内容
const splitContent = computed(() => {
  if (!props.message) { return [] }

  // 按空行拆分段落
  const paragraphs = props.message.split(/\n\s*\n/)

  return paragraphs.map((para) => {
    // 判断段落是否需要markdown处理
    const needsMarkdown = /^#|\* |- |`{3}|\[.*\]\(.*\)/.test(para.trim())
    return {
      content: para,
      needsMarkdown,
    }
  })
})
</script>

<template>
  <view
    v-for="(item, index) in splitContent"
    :key="index"
    @touchstart="startTimer"
    @touchend="clearTimer"
    @mousedown="startTimer"
    @mouseup="clearTimer"
    @mouseleave="clearTimer"
    @contextmenu.prevent
  >
    <UaMarkdown
      v-if="item.needsMarkdown"
      :source="item.content"
      :autoWrap="true"
      :showLine="true"
    />
    <PlainText
      v-else
      :text="item.content"
    />
  </view>
</template>
