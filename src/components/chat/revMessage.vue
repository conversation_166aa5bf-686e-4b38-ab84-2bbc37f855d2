<script setup lang="ts">
// import VerResponse from '@/components/ver-response/ver-response/ver-response.vue'
import Markdown from '@/components/markdown/markdown.vue'

const props = defineProps({
  message: {
    type: String,
    required: true,
  },
  openingStatement: {
    type: Boolean,
    default: false,
  },
  isRequesting: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['shareMessage'])

function handleShare() {
  emit('shareMessage', props.message)
}

function copyText(text: string) {
  text = text.replace(/>\s*💭.*\n>/gs, '').trim()
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}
</script>

<template>
  <view class="ai-message">
    <view class="message-bubble">
      <view v-if="!openingStatement" class="message-content">
        <!-- 加载中指示器 -->
        <view v-if="isRequesting" class="loading-indicator">
          <view class="loading-dots">
            <view class="dot" />
            <view class="dot" />
            <view class="dot" />
          </view>
        </view>
        <!-- <mpHtml v-else :content="message" :markdown="true" /> -->
        <!-- <VerResponse v-else theme="vsinger" :content="message" themeColor="#3B82F6" /> -->
        <Markdown :message="message" />
        <view class="message-actions">
          <view class="action-button" @click="copyText(message)">
            <uni-icons fontFamily="CustomFont" size="16" color="#64748B">
              {{ '\ue63d' }}
            </uni-icons>
          </view>
          <view>
            <button v-if="true" class="action-button action-share" open-type="share" @click="handleShare">
              <uni-icons type="upload" size="16" color="#64748B" />
            </button>
          </view>
        </view>
      </view>
      <view v-else>
        <Markdown :message="message" />
        <!-- <VerResponse theme="heavyAnime" :content="message" themeColor="#3B82F6" /> -->
      </view>
    </view>
  </view>
</template>

<style scoped>
.ai-message {
  @apply mb-6 flex w-full;
}

.message-bubble {
  @apply flex w-full max-w-md flex-col rounded-2xl rounded-tl-none bg-white px-2 py-3 shadow-sm;
  border: 1px solid rgba(226, 232, 240, 0.8);
  position: relative;
  transition: all 0.2s ease;
}

.message-content {
  @apply mb-2 text-base font-normal text-gray-800 leading-8;
}

.message-actions {
  @apply flex justify-end space-x-2;
}

.action-button {
  @apply flex h-7 w-7 items-center justify-center rounded-full transition-all duration-200;
}

.action-button:active {
  @apply bg-gray-100;
}
.action-share {
  @apply after:border-none bg-transparent;
}
/* 加载中指示器 */
.loading-indicator {
  @apply flex justify-center my-4;
}

.loading-dots {
  @apply flex space-x-1;
}

.dot {
  @apply w-2 h-2 bg-blue-500 rounded-full;
  animation: bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
