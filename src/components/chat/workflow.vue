<script setup lang="ts">
import ChatTitle from '@/components/chat/ChatTitle.vue'
import Drawer from '@/components/chat/Drawer.vue'
import MessageSender from '@/components/chat/MessageSender.vue'
import RechargeTips from '@/components/chat/rechargeTips.vue'
import RevMessage from '@/components/chat/revMessage.vue'
import UserMessage from '@/components/chat/userMessage.vue'
import { useDifyChatStore } from '@/stores/dify-chat'
import { storeToRefs } from 'pinia'
import { nextTick, onMounted, ref } from 'vue'

const props = defineProps({
  id: String,
  agentName: String,
  type: String,
})

const titleHeight = ref(0)
const sendHeight = ref(0)
// 抽屉状态管理
const drawerOpen = ref(false)
const animationState = ref('closed')

const newMessage = ref('')
const sendStatus = ref(false)
// 充值提示窗口状态
const showRechargeTips = ref(false)
const difyStore = useDifyChatStore()
const scrollViewRef = ref(null)
// 从 dify store 中解构出所需的方法
const {
  initDifyApp, // 初始化 Dify 应用
  setAppId, // 设置应用 ID
  setType, // 设置类型
  startNewConversation, // 开始新对话
} = difyStore

// 滚动到底部
function scrollToBottom() {
  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    setTimeout(() => {
      // 直接使用页面滚动
      uni.pageScrollTo({
        scrollTop: 99999,
        duration: 300,
      })
    }, 100)
  })
}

// 关闭抽屉菜单
function closeDrawer() {
  animationState.value = 'closed'
  setTimeout(() => {
    drawerOpen.value = false
  }, 300) // 等待动画完成
}

// 从 store 中获取响应式状态
const {
  messages,
  isLoadingMessage, // 是否正在加载消息
  currentConversationId, // 当前会话 ID
  conversationHistory, // 会话历史列表
  isMessagesLoading, // 是否正在加载会话列表

} = storeToRefs(difyStore)

// 计算当前会话标题
const currentChatTitle = computed(() => {
  if (!currentConversationId.value) {
    return props.agentName || 'AI聊天助手'
  }

  // 从会话历史中查找当前会话
  const currentChat = conversationHistory.value.find(chat => chat.id === currentConversationId.value)
  return currentChat?.name || props.agentName || 'AI聊天助手'
})

// 监听消息变化，当加载新会话时滚动到底部
watch(messages, () => {
  if (messages.value.length > 0) {
    nextTick(() => {
      scrollToBottom()
    })
  }
})
// 分享相关数据
const shareParams = ref({
  title: '',
  path: '',
  imageUrl: '',
  desc: '',

})

// 发送消息
async function sendMessage(message: string) {
  if (!message.trim()) { return }

  // 设置发送状态
  sendStatus.value = true

  try {
    // 使用 difyStore 发送消息
    await difyStore.sendMessage(message)

    // 消息已发送，清空输入框
    newMessage.value = ''

    // 滚动到底部
    await nextTick()
    scrollToBottom()
  }
  catch (_error) {
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none',
    })
  }
  finally {
    // 重置发送状态
    sendStatus.value = false
  }
}

// 处理消息分享
function handleShareMessage(data: { content: string, shareType: string }) {
  // 设置要分享的内容
  shareParams.value.desc = data.content
  shareParams.value.title = `${props.agentName || '智能助手'}的回复`
  shareParams.value.path = `/pages/agent/ai?id=${props.id}&agentName=${props.agentName}`

  // 根据分享类型执行不同操作
  if (data.shareType === '微信好友' || data.shareType === '朋友圈') {
    // 微信分享会自动触发 onShareAppMessage 或 onShareTimeline
    uni.showShareMenu({
      withShareTicket: true,
      menuList: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        uni.showToast({
          title: '请点击右上角分享',
          icon: 'none',
        })
      },
    })
  }
  else if (data.shareType === '复制链接') {
    // 复制分享链接
    const shareLink = `${props.agentName}: ${data.content}`
    uni.setClipboardData({
      data: shareLink,
      success: () => {
        uni.showToast({
          title: '链接已复制',
          icon: 'none',
        })
      },
    })
  }
}

// 打开抽屉菜单
function openList() {
  drawerOpen.value = true
  setTimeout(() => {
    animationState.value = 'opened'
  }, 50)
}
function toggleDrawer() {
  if (drawerOpen.value) {
    closeDrawer()
  }
  else {
    openList()
  }
}

onShareAppMessage((_res) => {
  shareParams.value.title = `${props.agentName || '智能助手'}`
  shareParams.value.path = `/pages/agent/chat?id=${props.id}&agentName=${props.agentName}&conversationId=${currentConversationId.value}`
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: `${import.meta.env.VITE_SHAREIMG}`, // 分享图
    // desc: '小程序描述描述描述描述',
  }
})

onShareTimeline(() => {
  shareParams.value.title = `${props.agentName || '智能助手'}`
  shareParams.value.path = `/pages/agent/chat?id=${props.id}&agentName=${props.agentName}`
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: `${import.meta.env.VITE_SHAREIMG}`, // 分享图
  }
})

onLoad(() => {
  // 可以在这里添加页面加载时的逻辑

})
// 初始化
onMounted(async () => {
  // 初始化逻辑
  // 设置默认分享路径
  if (props.id) {
    setAppId(props.id)
  }

  if (props.type) {
    setType(props.type as 'chat' | 'completion' | 'workflow')
  }

  try {
    // 初始化 Dify 应用
    await initDifyApp()

    // 如果有消息，滚动到底部
    if (messages.value.length > 0) {
      await nextTick()
      scrollToBottom()
    }
  }
  catch (_error) {
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none',
    })
  }
})

function setSendHeight(int: number) {
  sendHeight.value = int
}

function setTitleHeight(int: number) {
  titleHeight.value = int
}

// 处理充值提示窗口关闭
function handleRechargeTipsClose() {
  showRechargeTips.value = false
}

function handleNewChat() {
  startNewConversation()
}
</script>

<template>
  <view class="chat-page">
    <!-- 主聊天区域 -->
    <view
      id="chatbox" class="chat-main-container"
      :style="{ transform: animationState === 'opened' ? 'scale(0.95) translateX(300px)' : '' }"
    >
      <!-- 聊天标题栏 -->
      <ChatTitle
        :title="currentChatTitle" @toggle-drawer="toggleDrawer" @setTitleHeight="setTitleHeight"
        @newChat="handleNewChat"
      />

      <!-- 聊天消息区域 -->
      <scroll-view
        id="chatMessages"
        ref="scrollViewRef"
        class="chat-messages-container"
        scroll-y="true"
        enable-back-to-top="true"
        scroll-with-animation="true"
      >
        <view class="messages-wrapper">
          <!-- 消息列表 -->
          <template v-for="(message, index) in messages" :key="message.id">
            <!-- 用户消息 -->
            <UserMessage v-if="!message.isBot" :message="message.content" />
            <!-- AI消息 -->
            <RevMessage
              v-else
              :message="message.content"
              :loadingMessage="isLoadingMessage && index === messages.length - 1"
              @share-message="handleShareMessage"
            />
          </template>

          <!-- 底部空白区域，确保最后一条消息不被输入框遮挡 -->
          <view class="messages-bottom-space" />
        </view>
      </scroll-view>
      <RechargeTips v-if="showRechargeTips" :visible="showRechargeTips" @close="handleRechargeTipsClose" />
      <!-- 消息输入区域 -->
      <MessageSender :sendStatus="isLoadingMessage" @send="sendMessage" @setHeight="setSendHeight" />
    </view>

    <!-- 抽屉菜单 -->
    <view
      id="leftInfo" class="drawer-container" :class="{
        'drawer-opened': drawerOpen && animationState === 'opened',
        'drawer-closed': !drawerOpen || animationState === 'closed',
      }"
    >
      <Drawer @closeDrawer="closeDrawer" @newChat="handleNewChat" />
    </view>

    <!-- 遮罩层 -->
    <view
      v-if="drawerOpen" class="drawer-overlay" :class="{ 'overlay-visible': animationState === 'opened' }"
      @click="closeDrawer"
    />
  </view>
</template>

<style scoped lang="scss">
@import url(./chat.scss);
</style>
