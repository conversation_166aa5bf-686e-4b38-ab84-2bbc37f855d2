<script setup lang="ts">
import ChatWorkflow from '@/components/chat/ChatWorkflow.vue'
// import verResponse from '../ver-response/ver-response/ver-response.vue'
import Markdown from '@/components/markdown/markdown.vue'
import { useDifyChatStore } from '@/stores/dify-chat'

const difyChatStore = useDifyChatStore()
const {
  generatedContent,
  workflowContent,
  workflowNodes,
  isLoadingMessage,
  type,
} = storeToRefs(difyChatStore)

const isSaving = ref(false)

const isWorkflow = computed(() => {
  return type.value === 'workflow'
})

const isCompletion = computed(() => {
  return type.value === 'completion'
})

const isShowEmptyState = computed(() => {
  // return false
  if (isCompletion.value) {
    return !generatedContent.value
  }
  if (isWorkflow.value) {
    // 使用 workflowContent 判断是否为空
    return !workflowNodes.value.length && !workflowContent.value
  }
  return true
})

// 简化内容显示逻辑
const contentToShow = computed((): string => {
  if (isWorkflow.value) {
    return workflowContent.value || ''
  }
  return generatedContent.value || ''
})

const wordCount = computed(() => {
  if (!contentToShow.value) { return 0 }
  return contentToShow.value.length
})

async function copyContent() {
  if (!contentToShow.value) { return }
  try {
    uni.setClipboardData({
      data: contentToShow.value,
      success: () => {
        uni.showToast({
          title: '复制成功',
          icon: 'none',
        })
      },
    })
  }
  catch (error) {
    uni.showToast({
      title: '无法复制内容',
      duration: 2000,
    })
  }
}

async function saveContent() {
  if (!contentToShow.value || isSaving.value) { return }
  isSaving.value = true
  try {
    await difyChatStore.saveCompletionContent()
  }
  catch (err) {
    console.error('保存内容出错:', err)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <view class="flex size-full flex-col rounded-lg border bg-white">
    <!-- Empty State -->
    <view v-if="isShowEmptyState" class="flex flex-1 items-center justify-center text-gray-500">
      <view class="text-center">
        <view class="mb-4">
          <uni-icons type="chatboxes" size="40" color="#3B82F6" />
        </view>
        <p>{{ isLoadingMessage ? "生成中...." : "AI会在这里给你惊喜。" }}</p>
      </view>
    </view>

    <!-- Content State -->
    <view v-else class="flex flex-1 flex-col overflow-hidden p-6">
      <!-- Workflow Nodes -->
      <ChatWorkflow v-if="type === 'workflow'" :nodes="workflowNodes" />

      <!-- Markdown Content -->
      <view class="mb-6 flex-1 overflow-auto">
        <!-- <verResponse theme="vsinger" :content="contentToShow" themeColor="#3B82F6" /> -->
        <Markdown :message="contentToShow" />
      </view>

      <!-- Action Bar -->
      <view class="mt-auto flex items-center justify-between border-t pt-4">
        <view class="text-sm text-gray-500">
          {{ wordCount }} 个字符
        </view>
        <view class="flex items-center gap-4">
          <button size="sm" class="gap-2 bg-blue-600 py-1 text-base text-white" @click="copyContent">
            <uni-icons fontFamily="CustomFont" size="16" color="#fff">
              {{ '\ue63d' }}
            </uni-icons>
            复制
          </button>
          <button
            size="sm" class="gap-2 bg-blue-600 py-1 text-base text-white" :disabled="isSaving"
            @click="saveContent"
          >
            <uni-icons v-if="isSaving" type="spinner-cycle" size="16" color="#FFFFFF" class="animate-spin" />
            <uni-icons v-else type="wallet" size="16" color="#FFFFFF" />
            {{ isSaving ? "保存中..." : "保存" }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
