import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const useCategoryStore = defineStore('category', () => {
  const error = ref<string | null>(null)

  /**
   * 分页获取应用类别列表
   * @param page - 当前页码，从 1 开始
   * @param size - 每页显示的记录数量
   * @returns 返回包含分页信息和应用类别列表的 JSON 对象
   * @throws 如果用户未登录，抛出包含错误信息的 Error 对象；如果查询过程中出现错误，抛出包含错误信息的 Error 对象
   */
  async function getAppCategories(page: number, size: number) {
    const { data, error } = await supabase
      .rpc('app_category_list', {
        p_page: page,
        p_size: size,
      })

    if (error) {
      throw new Error(`Error getting app category list: ${error.message}`)
    }
    return data
  }

  /**
   * 获取应用类别详情
   * @param id - 类别id
   * @returns 返回包含应用类别信息的 JSON 对象
   * @throws 如果用户未登录，抛出包含错误信息的 Error 对象；如果查询过程中出现错误，抛出包含错误信息的 Error 对象
   */
  async function getAppCategory(id: string) {
    const { data, error } = await supabase
      .rpc('app_category_get', {
        p_category_id: id,
      })
      .single()

    if (error) {
      throw new Error(`Error getting app category: ${error.message}`)
    }
    return data
  }

  /**
   * 创建应用类别
   * @param title - 类别标题
   * @param description - 类别描述
   * @returns 返回包含新创建应用类别信息的 JSON 对象
   * @throws 如果用户未登录，抛出包含错误信息的 Error 对象；如果创建过程中出现错误，抛出包含错误信息的 Error 对象
   */
  async function createAppCategory(
    title: string,
    description: string,
    sort: number,
  ) {
    // 检查标题参数
    if (!title || title.trim() === '') {
      throw new Error('标题不能为空')
    }

    // 检查描述参数
    if (!description || description.trim() === '') {
      throw new Error('描述不能为空')
    }

    const { data, error } = await supabase
      .rpc('app_category_create', {
        p_title: title,
        p_description: description,
        p_sort: sort,
      })
      .single()

    if (error) {
      throw new Error(`Error creating app category: ${error.message}`)
    }
    return data
  }

  /**
   * 更新应用类别
   * @param id - 类别id
   * @param title - 类别标题
   * @param description - 类别描述
   * @returns 返回包含更新后应用类别信息的 JSON 对象
   * @throws 如果用户未登录，抛出包含错误信息的 Error 对象；如果更新过程中出现错误，抛出包含错误信息的 Error 对象
   */
  async function updateAppCategory(
    id: string,
    title: string,
    description: string,
    sort: number,
  ) {
    // 检查ID参数
    if (!id || id.trim() === '') {
      throw new Error('类别ID不能为空')
    }

    // 检查标题参数
    if (!title || title.trim() === '') {
      throw new Error('标题不能为空')
    }

    // 检查描述参数
    if (!description || description.trim() === '') {
      throw new Error('描述不能为空')
    }
    const { data, error } = await supabase
      .rpc('app_category_update', {
        p_category_id: id,
        p_title: title,
        p_description: description,
        p_sort: sort,
      })

    if (error) {
      throw new Error(`Error updating app category: ${error.message}`)
    }
    return data
  }

  /**
   * 删除应用类别
   * @param id - 类别id
   * @throws 如果用户未登录，抛出包含错误信息的 Error 对象；如果删除过程中出现错误，抛出包含错误信息的 Error 对象
   */
  async function deleteAppCategory(id: string) {
    const { error } = await supabase
      .rpc('app_category_delete', {
        p_category_id: id,
      })

    if (error) {
      throw new Error(`Error deleting app category: ${error.message}`)
    }
  }

  return {
    getAppCategories,
    getAppCategory,
    createAppCategory,
    updateAppCategory,
    deleteAppCategory,
  }
})
