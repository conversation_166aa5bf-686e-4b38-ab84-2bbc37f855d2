import type { LLMPlatformConfig } from '@/types/company'
import { supabase } from '@/services/supabase'

import { defineStore } from 'pinia'

const BASE_URL = import.meta.env.VITE_DOMAIN
export const useInviteLinkStore = defineStore('groupInvite', () => {
  const client = supabase

  // 检查用户是否已登录
  async function checkUserLoggedIn() {
    const { data: userData } = await supabase.auth.getUser()
    if (!userData.user) {
      throw new Error('User is not logged in')
    }
    return userData.user
  }

  // 1. 创建邀请用户创建空间的链接
  async function createInviteLinkForCreatingCompany(
    llmPlatforms: LLMPlatformConfig[],
  ) {
    await checkUserLoggedIn()
    const { data, error } = await client.rpc('invite_link_create', {
      p_invite_type: 'invite_to_create_company',
      p_llm_platform: llmPlatforms,
      p_limit: 1,
    })
    if (error) {
      throw new Error(
        `Error creating invite link for creating company: ${error.message}`,
      )
    }
    return data
  }

  // 2. 创建邀请管理空间的链接
  async function createInviteLinkForManagingCompany(companyId: string) {
    await checkUserLoggedIn()
    const { data, error } = await client.rpc('invite_link_create', {
      p_invite_type: 'invite_to_manage_company',
      p_company_id: companyId,
      p_limit: 1,
    })
    if (error) {
      throw new Error(
        `Error creating invite link for managing company: ${error.message}`,
      )
    }
    // 返回创建的邀请链接记录数据
    return data
  }

  // 3. 创建邀请加入空间的链接
  async function createInviteLinkForJoiningCompany(invite_type: string = 'invite_to_join_company', companyId: string, limit: number, remark: string, uid: string) {
    await checkUserLoggedIn()
    console.log('limit', limit)
    const { data, error } = await client.rpc('invite_link_create', {
      p_invite_type: invite_type,
      p_company_id: companyId,
      p_limit: limit,
      p_remark: remark,
      p_creator_uid: uid,
    })
    if (error) {
      throw new Error(
        `Error creating invite link for joining company: ${error.message}`,
      )
    }
    // 返回创建的邀请链接记录数据
    return data
  }

  // 4. 通过邀请链接创建空间
  async function createCompanyByInviteLink(code: string, name: string, icon: string, description: string) {
    await checkUserLoggedIn()
    const { data, error } = await client.rpc('invite_link_create_company', {
      p_code: code,
      p_name: name,
      p_icon: icon,
      p_introduction: description,
    })
    if (error) {
      throw new Error(
        `${error.data.message}`,
      )
    }
    return data
  }

  // 5. 加入并管理空间
  async function joinAndManageCompanyByInviteLink(inviteLinkId: string) {
    await checkUserLoggedIn()
    const { data, error } = await client.rpc('invite_link_join_manage', {
      p_invite_link_id: inviteLinkId,
    })
    if (error) {
      throw new Error(
        `${error.data.message}`,
      )
    }
    return data
  }

  // 6. 加入空间
  async function joinCompanyByInviteLink(code: string, uid: string) {
    console.log('获取的link_id', code)
    console.log('获取的uid', uid)
    await checkUserLoggedIn()
    const { data, error } = await client.rpc('invite_link_join', {
      p_code: code,
      p_uid: uid,
    })
    if (error) {
      console.log(error, 'errorerrorerrorerror')

      throw new Error(
        error.data.message,
      )
    }
    console.log('data', data)
    console.log('error', error)
    return data
  }

  // 7 - 8. 启用/禁用链接
  async function toggleInviteLinkStatus(
    inviteLinkId: string,
    isDisabled: boolean,
  ) {
    await checkUserLoggedIn()
    const { error } = await client.rpc('invite_link_toggle_status', {
      p_id: inviteLinkId,
      p_is_disabled: isDisabled,
    })
    if (error) {
      throw new Error(
        `Error toggling invite link status: ${error.message}`,
      )
    }
  }

  async function enableInviteLink(inviteLinkId: string) {
    return toggleInviteLinkStatus(inviteLinkId, false)
  }

  async function disableInviteLink(inviteLinkId: string) {
    return toggleInviteLinkStatus(inviteLinkId, true)
  }

  // // 9. 获取邀请链接
  // async function getInviteLinks(page: number, size: number) {
  //   await checkUserLoggedIn()
  //   const adjustedPage = page - 1
  //   const {
  //     data: inviteLinks,
  //     error: inviteLinkError,
  //     count,
  //   } = await client.rpc('', {})

  //   if (inviteLinkError) {
  //     throw new Error(
  //       `Error fetching invite links: ${inviteLinkError.message}`,
  //     )
  //   }

  //   const companyIds = inviteLinks
  //     .map((link: any) => link.company_id)
  //     .filter((id: any) => id)

  //   const { data: companies, error: companyError } = await client.rpc('', {})

  //   if (companyError) {
  //     throw new Error(`Error fetching companies: ${companyError.message}`)
  //   }

  //   const companyMap = new Map()
  //   companies.forEach((company: any) => {
  //     companyMap.set(company.id, company.name)
  //   })

  //   const result = inviteLinks.map((link: any) => {
  //     const companyName = companyMap.get(link.company_id) || ''
  //     const inviteUrl = `https://at.racio.chat/account/activate?token=${link.id}`
  //     // 生成链接列表的id
  //     const id = link.id
  //     // 获取邀请链接的创建时间
  //     const createdAt = link.created_at
  //     const limit = link.limit
  //     // 获取链接可用还是失效
  //     const isDisabled = link.is_disabled
  //     // 获取邀请链接的备注信息
  //     const remark = link.remark ? link.remark : ''
  //     return { companyName, inviteUrl, id, createdAt, limit, isDisabled, remark }
  //   })

  //   return { data: result, count }
  // }

  // 10. 判断邀请链接是否可用
  async function isInviteLinkAvailable(inviteLinkId: string) {
    await checkUserLoggedIn()
    const { data: inviteLink, error: inviteLinkError } = await client.rpc('fn', {})

    if (inviteLinkError) {
      throw new Error(`Error fetching invite link: ${inviteLinkError.message}`)
    }

    if (!inviteLink) {
      return false
    }

    return !inviteLink.is_disabled && inviteLink.usaged < inviteLink.limit
  }

  // 获取链接列表
  async function getInviteLinks(
    page: number,
    size: number,
    companyId?: string,
  ) {
    const { data, error } = await client.rpc('get_invite_links', {
      p_page: page,
      p_size: size,
      p_company_id: companyId || null,
    })

    if (error) {
      throw new Error(`Error fetching invite links: ${error.message}`)
    }
    let processedData = []
    // 处理返回数据，添加前端需要生成的字段
    if (data.data) {
      processedData = data.data.map((link: any) => ({
        ...link,
        invite_url: getInviteLinkById(
          link.id,
          link.invite_type,
          link.company_id || '',
          link.company_name || '',
        ),
        cnRole: getCnRoleFromInviteType(link.invite_type),
      }))
    }

    return {
      data: processedData,
      count: data.count,
    }
  }
  function getInviteLinkById(
    id: string,
    inviteType: string,
    companyId: string,
    companyName: string,
  ) {
    const INVITE_BASE_URL
      = `${BASE_URL}/dashboard/ai/inviteOpen?`
    const role = getRoleFromInviteType(inviteType)
    const inviteUrl = `${INVITE_BASE_URL}id=${id}&role=${role}&company_id=${companyId}&company_name=${companyName}`
    return inviteUrl
  }

  // 根据邀请类型获取对应的角色
  function getRoleFromInviteType(inviteType: string): string {
    switch (inviteType) {
      case 'invite_to_create_company':
        return 'companyowner'
      case 'invite_to_manage_company':
        return 'companyadmin'
      case 'invite_to_join_company':
        return 'user'
      default:
        return 'unknown'
    }
  }
  function getCnRoleFromInviteType(inviteType: string): string {
    switch (inviteType) {
      case 'invite_to_create_company':
        return '空间所有者'
      case 'invite_to_manage_company':
        return '空间管理员'
      case 'invite_to_join_company':
        return '会员'
      default:
        return '未知角色'
    }
  }

  return {
    createInviteLinkForCreatingCompany,
    createInviteLinkForManagingCompany,
    createInviteLinkForJoiningCompany,
    createCompanyByInviteLink,
    joinAndManageCompanyByInviteLink,
    joinCompanyByInviteLink,
    enableInviteLink,
    disableInviteLink,
    getInviteLinks,
    isInviteLinkAvailable,
    toggleInviteLinkStatus,
  }
})
