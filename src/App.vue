<script setup lang="ts">
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { globalDataStore } from '@/stores/globalData'
import { useMemoryStore } from '@/stores/memory'
import { Base64 } from 'js-base64'

const authStore = useAuthStore()
const useGlobalData = globalDataStore()

const { initialize, updatePublicUser, clearAuth, refreshSession, initLogin } = authStore
const { initializeSystemInfo, checkPlatform } = useGlobalData

const { user, loginStatus, wxAuth } = storeToRefs(authStore)

onShow(async () => {
  // 监听Auth状态变化
  supabase.auth.onAuthStateChange(async (event, session) => {
    switch (event) {
      case 'SIGNED_IN':
        // 监听登录
        // if (!session?.user.phone) {
        //   uni.reLaunch({
        //     url: '/pages/auth/bindPhone',
        //   })
        // }
        // else {
        // setTimeout(() => {
        //   let pages = getCurrentPages()
        //   let page = pages[pages.length - 1]
        //   console.log(page, 'page')
        //   uni.navigateBack({
        //     delta: 1,
        //   })
        // }, 300)
        // }

        if (session) {
          await updatePublicUser(session.user, true)
        }
        break
      case 'TOKEN_REFRESHED':
        console.log('TOKEN_REFRESHED')
        // 监听令牌刷新
        break
      case 'SIGNED_OUT':
        console.log('SIGNED_OUT')
        break
      case 'USER_UPDATED':

        if (session) {
          user.value = session.user
          uni.setStorageSync('user', session.user)
        }

        console.log('USER_UPDATED')

        // 监听用户的更新信息
        break

      case 'INITIAL_SESSION':
        console.log(event, 'INITIAL_SESSIONINITIAL_SESSION', session)

        if (!session) {
          initLogin()

          return
        }

        {
          const arrayBuffer = JSON.stringify(session)
          const wxAuthtxt = `sb-${import.meta.env.VITE_MEMFIREDB_ID}-auth-token=base64-${Base64.encodeURL(arrayBuffer)}`
          uni.setStorageSync('wxAuth', wxAuthtxt)

          user.value = session?.user || {}
          wxAuth.value = wxAuthtxt

          // if (!session.user.phone) {
          //   uni.reLaunch({
          //     url: '/pages/auth/bindPhone',
          //   })
          // }
        }

        break
      default:
        console.log(event, session, 'onAuthStateChange')

        break
    }
  })

  // 初始化用户信息l
  await initialize()
  checkPlatform()
  // #ifdef MP-WEIXIN
  await initializeSystemInfo()
  // #endif
})

onHide(() => {
  console.log('App Hide')
})
onError((err) => {
  console.log('App Error', err)
})
</script>

<style lang="scss">
@use 'tailwindcss/base';
@use 'tailwindcss/components';
@use 'tailwindcss/utilities';

@font-face {
  font-family: CustomFont;
  src: url('@/static/iconfont.ttf');
}
/*  #ifdef  H5  */
uni-page-head {
  display: none;
}
svg {
  display: initial;
}

/*  #endif  */

@layer components {
  .raw-btn {
    // 注意 after: 后面不能加任何空格，有些格式化工具可能会在这里自动加一个空格
    @apply after:border-none inline-flex items-center gap-2 rounded text-sm font-semibold transition-all;
  }

  .btn {
    // 使用上面定义的 raw-btn
    @apply raw-btn bg-gradient-to-r from-[#9e58e9] to-blue-500 px-2 py-1 text-white;
  }
}
</style>
